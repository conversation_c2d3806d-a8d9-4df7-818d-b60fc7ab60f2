/*
 * @Author: 47844 <EMAIL>
 * @Date: 2025-01-16 16:00:00
 * @LastEditors: 47844 <EMAIL>
 * @LastEditTime: 2025-09-23 14:09:09
 * @FilePath: \dst-front\packages\key-hmes-front\src\routes\WeighingRecordReport\components\RollInfoTable.tsx
 * @Description: 检斤记录表
 */
import React, { useEffect, useMemo, useState } from 'react';
import {
  Table,
  DataSet,
  Tooltip,
  Button,
  Modal,
  Form,
  TextField,
  DateTimePicker,
  Select,
  Output,
  NumberField,
} from 'choerodon-ui/pro';
import { ColumnAlign } from 'choerodon-ui/pro/lib/table/enum';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import withProps from 'utils/withProps';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import request from 'utils/request';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import { rollInfoDS, formDS, returnAdjustDS, optionsDs } from '../stores';
import { mockRollInfoData } from '../mock';
import styles from '../index.module.less';

const modelPrompt = 'hspc.weighingRecordReport';
const tenantId = getCurrentOrganizationId();

const RollInfoTable = ({ rollInfoDs, queryParams }) => {
  const [selectedRow, setSelectedRow] = useState<any>(null);
  const formDs = useMemo(() => new DataSet(formDS()), []);
  const returnAdjustDs = useMemo(() => new DataSet(returnAdjustDS()), []);
  const processRollInfoData = data => {
    if (!data || !data?.length) return [];

    const dateGroups = new Map<string, any[]>();
    data.forEach(item => {
      const key = item.shiftDate || '';
      if (!dateGroups.has(key)) dateGroups.set(key, []);
      dateGroups.get(key)!.push(item);
    });

    const processedData: any[] = [];

    dateGroups.forEach(itemsOfDate => {
      const teamGroups = new Map<string, any[]>();
      itemsOfDate.forEach(item => {
        const teamKey = `${item.shiftDate || ''}-${item.shiftTeamName || ''}-${item.shiftCode ||
          ''}`;
        if (!teamGroups.has(teamKey)) teamGroups.set(teamKey, []);
        teamGroups.get(teamKey)!.push(item);
      });

      teamGroups.forEach(teamItems => {
        teamItems.forEach((item, index) => {
          processedData.push({ ...item, lineNumber: index + 1 });
        });
        if (teamItems.length > 0) {
          const { shiftDate, shiftTeamName, shiftCode } = teamItems[0];
          processedData.push({
            shiftDate,
            shiftTeamName,
            shiftCode,
            isEmptyRow: true,
            className: styles.emptyRow,
          });
        }
      });
    });
    if (processedData.length > 0) {
      processedData.push({
        shiftCode: '当日生产合计',
        materialLotCode: '合格卷数',
        materialName: '待认定卷数',
        loadTime: '报废',
        unloadTime: '降级',
        splicingOrder: '异常卷',
        isGrandTotal: true,
        className: styles.grandTotal,
      });
    }

    return processedData;
  };

  const isDataRow = rec => !rec.get('isEmptyRow') && !rec.get('isGrandTotal');
  const getTeamRecords = record =>
    rollInfoDs.records.filter(
      item =>
        isDataRow(item) &&
        item.get('shiftDate') === record.get('shiftDate') &&
        item.get('shiftTeamName') === record.get('shiftTeamName') &&
        item.get('shiftCode') === record.get('shiftCode'),
    );
  const getAllRecords = () => rollInfoDs.records.filter(r => isDataRow(r));
  const sumField = (records: any[], field: string) =>
    records.reduce((total, rec) => total + (Number(rec.get(field)) || 0), 0);

  const columns = [
    {
      title: '卷号信息',
      children: [
        {
          name: 'identification',
          align: ColumnAlign.center,
          width: 180,
          renderer: props => {
            const { record } = props;
            if (record?.get('isGrandTotal')) {
              return <span style={{ color: '#000', fontWeight: 'bold' }}></span>;
            }
            if (record?.get('isEmptyRow')) {
              const recs = getTeamRecords(record);
              const validSalesRollCount = recs.filter(
                rec => rec.get('identification') && rec.get('identification').trim() !== '',
              ).length;
              return (
                <span style={{ color: '#000' }}>本班生产小计 （{validSalesRollCount}卷）</span>
              );
            }
            return record?.get('identification');
          },
          onCell: ({ record }) => {
            if (record?.get('isGrandTotal')) {
              return {
                colSpan: 0,
                hidden: true,
              };
            }
            return {};
          },
        },
        {
          name: 'materialLotCode',
          align: ColumnAlign.center,
          width: 180,
          renderer: props => {
            const { record } = props;
            if (record?.get('isGrandTotal')) {
              // 统计所有小计行的合格卷数汇总
              const allTeamRecords = rollInfoDs.records.filter(r => r.get('isEmptyRow'));
              let totalPassedRollCount = 0;
              allTeamRecords.forEach(teamRecord => {
                const teamRecs = getTeamRecords(teamRecord);
                const passedRollCount = teamRecs.filter(
                  rec => rec.get('dispositionFunction') === '通过',
                ).length;
                totalPassedRollCount += passedRollCount;
              });
              return (
                <span style={{ color: '#000', fontWeight: 'bold' }}>
                  合格卷数（{totalPassedRollCount}卷）
                </span>
              );
            }
            if (record?.get('isEmptyRow')) {
              const recs = getTeamRecords(record);
              const passedRollCount = recs.filter(rec => rec.get('dispositionFunction') === '通过')
                .length;
              return <span style={{ color: '#000' }}>合格卷数（{passedRollCount}卷）</span>;
            }
            return record?.get('materialLotCode');
          },
          onCell: ({ record }) => {
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return {
                colSpan: 3,
              };
            }
            return {};
          },
        },
        {
          name: 'oldVolumeNumber',
          align: ColumnAlign.center,
          width: 120,
          renderer: props => {
            const { record } = props;
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return null;
            }
            return record?.get('oldVolumeNumber');
          },
          onCell: ({ record }) => {
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return {
                colSpan: 0,
                hidden: true,
              };
            }
            return {};
          },
        },
        {
          name: 'dispositionFunction',
          align: ColumnAlign.center,
          width: 80,
          renderer: props => {
            const { record } = props;
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return null;
            }
            return record?.get('dispositionFunction');
          },
          onCell: ({ record }) => {
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return {
                colSpan: 0,
                hidden: true,
              };
            }
            return {};
          },
        },
        {
          name: 'materialName',
          align: ColumnAlign.center,
          width: 200,
          renderer: props => {
            const { record } = props;
            if (record?.get('isGrandTotal')) {
              const allTeamRecords = rollInfoDs.records.filter(r => r.get('isEmptyRow'));
              let totalPendingRollCount = 0;
              allTeamRecords.forEach(teamRecord => {
                const teamRecs = getTeamRecords(teamRecord);
                const pendingRollCount = teamRecs.filter(
                  rec => rec.get('dispositionFunction') === '待认定',
                ).length;
                totalPendingRollCount += pendingRollCount;
              });
              return (
                <span style={{ color: '#000', fontWeight: 'bold' }}>
                  待认定卷数（{totalPendingRollCount}卷）
                </span>
              );
            }
            if (record?.get('isEmptyRow')) {
              const recs = getTeamRecords(record);
              const pendingRollCount = recs.filter(
                rec => rec.get('dispositionFunction') === '待认定',
              ).length;
              return <span style={{ color: '#000' }}>待认定卷数（{pendingRollCount}卷）</span>;
            }
            return record?.get('materialName');
          },
        },
        // {
        //   name: 'prodTargetValue',
        //   align: ColumnAlign.center,
        //   width: 80,
        // },
        {
          name: 'productionMeter',
          align: ColumnAlign.center,
          width: 70,
          renderer: props => {
            const { record } = props as any;
            if (record?.get('isGrandTotal')) {
              const recs = getAllRecords();
              const v = sumField(recs, 'productionMeter');
              return <span style={{ color: '#000', fontWeight: 'bold' }}>{v}</span>;
            }
            if (record?.get('isEmptyRow')) {
              const recs = getTeamRecords(record);
              const v = sumField(recs, 'productionMeter');
              return <span style={{ color: '#000' }}>{v}</span>;
            }
            return record?.get('productionMeter');
          },
        },
        {
          name: 'squareMeters',
          align: ColumnAlign.center,
          width: 70,
          renderer: props => {
            const { record } = props as any;
            if (record?.get('isGrandTotal')) {
              const recs = getAllRecords();
              const v = sumField(recs, 'squareMeters');
              return <span style={{ color: '#000', fontWeight: 'bold' }}>{v}</span>;
            }
            if (record?.get('isEmptyRow')) {
              const recs = getTeamRecords(record);
              const v = sumField(recs, 'squareMeters');
              return <span style={{ color: '#000' }}>{v}</span>;
            }
            return record?.get('squareMeters');
          },
        },
        {
          name: 'rollUpTime',
          align: ColumnAlign.center,
          width: 120,
          renderer: props => {
            const { record } = props;
            if (record?.get('isGrandTotal')) {
              // 统计所有小计行的报废卷数汇总
              const allTeamRecords = rollInfoDs.records.filter(r => r.get('isEmptyRow'));
              let totalScrapRollCount = 0;
              allTeamRecords.forEach(teamRecord => {
                const teamRecs = getTeamRecords(teamRecord);
                const scrapRollCount = teamRecs.filter(
                  rec => rec.get('dispositionFunction') === '报废',
                ).length;
                totalScrapRollCount += scrapRollCount;
              });
              return (
                <span style={{ color: '#000', fontWeight: 'bold' }}>
                  报废（{totalScrapRollCount}卷）
                </span>
              );
            }
            if (record?.get('isEmptyRow')) {
              const recs = getTeamRecords(record);
              // 统计卷状态为"报废"的记录数量
              const scrapRollCount = recs.filter(rec => rec.get('dispositionFunction') === '报废')
                .length;
              return <span style={{ color: '#000' }}>报废（{scrapRollCount}卷）</span>;
            }
            return record?.get('rollUpTime');
          },
        },
        {
          name: 'rollDownTime',
          align: ColumnAlign.center,
          width: 120,
          renderer: props => {
            const { record } = props;
            if (record?.get('isGrandTotal')) {
              // 统计所有小计行的降级卷数汇总
              const allTeamRecords = rollInfoDs.records.filter(r => r.get('isEmptyRow'));
              let totalDowngradeRollCount = 0;
              allTeamRecords.forEach(teamRecord => {
                const teamRecs = getTeamRecords(teamRecord);
                const downgradeRollCount = teamRecs.filter(
                  rec => rec.get('dispositionFunction') === '降级',
                ).length;
                totalDowngradeRollCount += downgradeRollCount;
              });
              return (
                <span style={{ color: '#000', fontWeight: 'bold' }}>
                  降级（{totalDowngradeRollCount}卷）
                </span>
              );
            }
            if (record?.get('isEmptyRow')) {
              const recs = getTeamRecords(record);
              // 统计卷状态为"降级"的记录数量
              const downgradeRollCount = recs.filter(
                rec => rec.get('dispositionFunction') === '降级',
              ).length;
              return <span style={{ color: '#000' }}>降级（{downgradeRollCount}卷）</span>;
            }
            return record?.get('rollDownTime');
          },
        },
        {
          name: 'volumeSequenceNum',
          align: ColumnAlign.center,
          width: 120,
          renderer: props => {
            const { record } = props;
            if (record?.get('isGrandTotal')) {
              // 统计所有小计行的异常卷数汇总
              const allTeamRecords = rollInfoDs.records.filter(r => r.get('isEmptyRow'));
              let totalAbnormalRollCount = 0;
              allTeamRecords.forEach(teamRecord => {
                const teamRecs = getTeamRecords(teamRecord);
                const abnormalRollCount = teamRecs.filter(
                  rec => rec.get('dispositionFunction') === '异常卷',
                ).length;
                totalAbnormalRollCount += abnormalRollCount;
              });
              return (
                <span style={{ color: '#000', fontWeight: 'bold' }}>
                  异常卷（{totalAbnormalRollCount}卷）
                </span>
              );
            }
            if (record?.get('isEmptyRow')) {
              const recs = getTeamRecords(record);
              // 统计卷状态为"异常卷"的记录数量
              const abnormalRollCount = recs.filter(
                rec => rec.get('dispositionFunction') === '异常卷',
              ).length;
              return <span style={{ color: '#000' }}>异常卷（{abnormalRollCount}卷）</span>;
            }
            return record?.get('volumeSequenceNum');
          },
          onCell: ({ record }) => {
            if (record?.get('isEmptyRow') || record?.get('isGrandTotal')) {
              return { colSpan: 2 };
            }
            return {};
          },
        },
      ],
    },
    {
      title: '边角料',
      children: [
        {
          name: 'backgroup',
          align: ColumnAlign.center,
          width: 100,
          onCell: ({ record }) => {
            if (record?.get('isEmptyRow') || record?.get('isGrandTotal')) {
              return { colSpan: 0, hidden: true };
            }
            return {};
          },
        },
        {
          name: 'backqty',
          align: ColumnAlign.center,
          width: 100,
          onCell: ({ record }) => {
            if (record?.get('isEmptyRow') || record?.get('isGrandTotal')) {
              return { colSpan: 0, hidden: true };
            }
            return {};
          },
        },
      ],
    },
    {
      title: '杂质',
      children: [
        {
          name: 'defectInformation1',
          header: '1',
          align: ColumnAlign.center,
          width: 60,
          renderer: props => {
            const { record } = props;
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return null;
            }
            return record?.get('defectInformation1');
          },
          onCell: ({ record }) => {
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return { colSpan: 9 };
            }
            return {};
          },
        },
        {
          name: 'defectInformation2',
          header: '2',
          align: ColumnAlign.center,
          width: 60,
          onCell: ({ record }) => {
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return { colSpan: 0, hidden: true };
            }
            return {};
          },
        },
        {
          name: 'defectInformation3',
          header: '3',
          align: ColumnAlign.center,
          width: 60,
          onCell: ({ record }) => {
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return { colSpan: 0, hidden: true };
            }
            return {};
          },
        },
        {
          name: 'defectInformation4',
          header: '4',
          align: ColumnAlign.center,
          width: 60,
          onCell: ({ record }) => {
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return { colSpan: 0, hidden: true };
            }
            return {};
          },
        },
      ],
    },
    {
      title: '接头',
      children: [
        {
          name: 'joint1',
          header: '1',
          align: ColumnAlign.center,
          width: 80,
          renderer: props => {
            const { record } = props;
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return <span style={{ color: '#000' }}></span>;
            }
            return record?.get('joint1');
          },
          onCell: ({ record }) => {
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return { colSpan: 0, hidden: true };
            }
            return {};
          },
        },
        {
          name: 'joint2',
          header: '2',
          align: ColumnAlign.center,
          width: 80,
          renderer: props => {
            const { record } = props;
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return null;
            }
            return record?.get('joint2');
          },
          onCell: ({ record }) => {
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return { colSpan: 0, hidden: true };
            }
            return {};
          },
        },
      ],
    },
    {
      title: '其他信息',
      children: [
        {
          name: 'packingMethodName',
          align: ColumnAlign.center,
          width: 100,
          renderer: props => {
            const { record } = props;
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return <span style={{ color: '#000' }}></span>;
            }
            return record?.get('packingMethodName');
          },
          onCell: ({ record }) => {
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return { colSpan: 0, hidden: true };
            }
            return {};
          },
        },
        {
          name: 'customerName',
          align: ColumnAlign.center,
          width: 120,
          renderer: props => {
            const { record } = props;
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return null;
            }
            return record?.get('customerName');
          },
          onCell: ({ record }) => {
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return { colSpan: 0, hidden: true };
            }
            return {};
          },
        },
        {
          name: 'grossWeight',
          align: ColumnAlign.center,
          width: 80,
          renderer: props => {
            const { record } = props as any;
            if (record?.get('isGrandTotal')) {
              const recs = getAllRecords();
              const v = sumField(recs, 'grossWeight');
              return <span style={{ color: '#000', fontWeight: 'bold' }}>{v}</span>;
            }
            if (record?.get('isEmptyRow')) {
              const recs = getTeamRecords(record);
              const v = sumField(recs, 'grossWeight');
              return <span style={{ color: '#000' }}>{v}</span>;
            }
            return record?.get('grossWeight');
          },
        },
        {
          name: 'corewEight',
          align: ColumnAlign.center,
          width: 80,
          renderer: props => {
            const { record } = props as any;
            if (record?.get('isGrandTotal')) {
              const recs = getAllRecords();
              const v = sumField(recs, 'corewEight');
              return <span style={{ color: '#000', fontWeight: 'bold' }}>{v}</span>;
            }
            if (record?.get('isEmptyRow')) {
              const recs = getTeamRecords(record);
              const v = sumField(recs, 'corewEight');
              return <span style={{ color: '#000' }}>{v}</span>;
            }
            return record?.get('corewEight');
          },
        },
        {
          name: 'isolationMembraneWeight',
          align: ColumnAlign.center,
          width: 80,
          renderer: props => {
            const { record } = props as any;
            if (record?.get('isGrandTotal')) {
              const recs = getAllRecords();
              const v = sumField(recs, 'isolationMembraneWeight');
              return <span style={{ color: '#000', fontWeight: 'bold' }}>{v}</span>;
            }
            if (record?.get('isEmptyRow')) {
              const recs = getTeamRecords(record);
              const v = sumField(recs, 'isolationMembraneWeight');
              return <span style={{ color: '#000' }}>{v}</span>;
            }
            return record?.get('isolationMembraneWeight');
          },
        },
        {
          name: 'aluminizingMembraneWeight',
          align: ColumnAlign.center,
          width: 80,
          renderer: props => {
            const { record } = props as any;
            if (record?.get('isGrandTotal')) {
              const recs = getAllRecords();
              const v = sumField(recs, 'aluminizingMembraneWeight');
              return <span style={{ color: '#000', fontWeight: 'bold' }}>{v}</span>;
            }
            if (record?.get('isEmptyRow')) {
              const recs = getTeamRecords(record);
              const v = sumField(recs, 'aluminizingMembraneWeight');
              return <span style={{ color: '#000' }}>{v}</span>;
            }
            return record?.get('aluminizingMembraneWeight');
          },
        },
        {
          name: 'netWeight',
          align: ColumnAlign.center,
          width: 80,
          renderer: props => {
            const { record } = props as any;
            if (record?.get('isGrandTotal')) {
              const recs = getAllRecords();
              const v = sumField(recs, 'netWeight');
              return <span style={{ color: '#000', fontWeight: 'bold' }}>{v}</span>;
            }
            if (record?.get('isEmptyRow')) {
              const recs = getTeamRecords(record);
              const v = sumField(recs, 'netWeight');
              return <span style={{ color: '#000' }}>{v}</span>;
            }
            return record?.get('netWeight');
          },
        },
        {
          name: 'detectionResults',
          align: ColumnAlign.center,
          width: 80,
          renderer: props => {
            const { record } = props as any;
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return null;
            }
            return record?.get('detectionResults');
          },
          onCell: ({ record }) => {
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return { colSpan: 12 };
            }
            return {};
          },
        },
        {
          name: 'coilingDiameter',
          align: ColumnAlign.center,
          width: 100,
          renderer: props => {
            const { record } = props;
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return null;
            }
            return record?.get('coilingDiameter');
          },
          onCell: ({ record }) => {
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return { colSpan: 0, hidden: true };
            }
            return {};
          },
        },
        {
          name: 'volumeWidth',
          align: ColumnAlign.center,
          width: 100,
          onCell: ({ record }) => {
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return { colSpan: 0, hidden: true };
            }
            return {};
          },
          renderer: props => {
            const { record } = props;
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return null;
            }
            return record?.get('volumeWidth');
          },
        },
        {
          name: 'surfaceFlatness',
          align: ColumnAlign.center,
          width: 100,
          renderer: props => {
            const { record } = props;
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return null;
            }
            return record?.get('surfaceFlatness');
          },
          onCell: ({ record }) => {
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return { colSpan: 0, hidden: true };
            }
            return {};
          },
        },
        {
          name: 'crossSectionFlatness',
          align: ColumnAlign.center,
          width: 100,
          renderer: props => {
            const { record } = props;
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return null;
            }
            return record?.get('crossSectionFlatness');
          },
          onCell: ({ record }) => {
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return { colSpan: 0, hidden: true };
            }
            return {};
          },
        },
        {
          name: 'selfTest1Name',
          align: ColumnAlign.center,
          width: 100,
          renderer: props => {
            const { record } = props;
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return null;
            }
            return record?.get('selfTest1Name');
          },
          onCell: ({ record }) => {
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return { colSpan: 0, hidden: true };
            }
            return {};
          },
        },
        {
          name: 'selfTest2Name',
          align: ColumnAlign.center,
          width: 100,
          renderer: props => {
            const { record } = props;
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return null;
            }
            return record?.get('selfTest2Name');
          },
          onCell: ({ record }) => {
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return { colSpan: 0, hidden: true };
            }
            return {};
          },
        },
        {
          name: 'lotNum',
          align: ColumnAlign.center,
          width: 100,
          renderer: props => {
            const { record } = props;
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return null;
            }
            return record?.get('lotNum');
          },
          onCell: ({ record }) => {
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return { colSpan: 0, hidden: true };
            }
            return {};
          },
        },
        {
          name: 'isolationMembraneTestName',
          align: ColumnAlign.center,
          width: 100,
          renderer: props => {
            const { record } = props;
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return null;
            }
            return record?.get('isolationMembraneTestName');
          },
          onCell: ({ record }) => {
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return { colSpan: 0, hidden: true };
            }
            return {};
          },
        },
        {
          name: 'inInventoryNum',
          align: ColumnAlign.center,
          width: 100,
          renderer: props => {
            const { record } = props;
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return null;
            }
            return record?.get('inInventoryNum');
          },
          onCell: ({ record }) => {
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return { colSpan: 0, hidden: true };
            }
            return {};
          },
        },
        {
          name: 'locatorType',
          align: ColumnAlign.center,
          width: 100,
          renderer: props => {
            const { record } = props;
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return null;
            }
            return record?.get('locatorType');
          },
          onCell: ({ record }) => {
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return { colSpan: 0, hidden: true };
            }
            return {};
          },
        },
        {
          name: 'netWeight',
          align: ColumnAlign.center,
          width: 100,
          renderer: props => {
            const { record } = props;
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return null;
            }
            return record?.get('netWeight');
          },
          onCell: ({ record }) => {
            if (record?.get('isGrandTotal') || record?.get('isEmptyRow')) {
              return { colSpan: 0, hidden: true };
            }
            return {};
          },
        },
        {
          name: 'receiveMeter',
          align: ColumnAlign.center,
          width: 100,
          renderer: props => {
            const { record } = props as any;
            if (record?.get('isGrandTotal')) {
              const recs = getAllRecords();
              const v = sumField(recs, 'receiveMeter');
              return <span style={{ color: '#000', fontWeight: 'bold' }}>{v}</span>;
            }
            if (record?.get('isEmptyRow')) {
              const recs = getTeamRecords(record);
              const v = sumField(recs, 'receiveMeter');
              return <span style={{ color: '#000' }}>{v}</span>;
            }
            return record?.get('receiveMeter');
          },
        },
      ],
    },
  ];

  const groups = useMemo(
    () => [
      {
        name: 'shiftDate',
        align: 'center',
        showTooltip: true,
        type: 'column',
        columnProps: {
          width: 120,
          align: 'center',
          title: '日期',
          lock: false,
          showTooltip: true,
          renderer: props => {
            const { record } = props;
            if (record?.get('isGrandTotal')) {
              return <span style={{ color: '#000', fontWeight: 'bold' }}></span>;
            }
            return record?.get('isEmptyRow') ? (
              <span style={{ color: '#000' }}></span>
            ) : (
              <Tooltip title={record?.get('shiftDate')}>
                <div style={{ textAlign: 'center', width: '100%' }}>{record?.get('shiftDate')}</div>
              </Tooltip>
            );
          },
        },
      },
      {
        name: 'shiftCode',
        align: 'center',
        showTooltip: true,
        type: 'column',
        columnProps: {
          width: 110,
          align: 'center',
          title: '班次',
          lock: false,
          showTooltip: true,
          renderer: props => {
            const { record } = props;
            if (record?.get('isGrandTotal')) {
              // 统计所有班组小计行的本班生产小计数量
              const allTeamRecords = rollInfoDs.records.filter(r => r.get('isEmptyRow'));
              let totalValidSalesRollCount = 0;
              allTeamRecords.forEach(teamRecord => {
                const teamRecs = getTeamRecords(teamRecord);
                const validSalesRollCount = teamRecs.filter(
                  rec => rec.get('identification') && rec.get('identification').trim() !== '',
                ).length;
                totalValidSalesRollCount += validSalesRollCount;
              });
              return (
                <div
                  style={{
                    color: '#000',
                    fontWeight: 'bold',
                    textAlign: 'center',
                    width: '100%',
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                >
                  当日生产合计 （{totalValidSalesRollCount}卷）
                </div>
              );
            }
            return record?.get('isEmptyRow') ? (
              <span style={{ color: '#000' }}></span>
            ) : (
              <Tooltip title={record?.get('shiftCode')}>
                <div style={{ textAlign: 'center', width: '100%' }}>{record?.get('shiftCode')}</div>
              </Tooltip>
            );
          },
          onCell: ({ record }) => {
            if (record?.get('isGrandTotal')) {
              return {
                colSpan: 3,
              };
            }
            return {};
          },
        },
      },
      {
        name: 'shiftTeamName',
        align: 'center',
        showTooltip: true,
        type: 'column',
        columnProps: {
          width: 100,
          align: 'center',
          title: '班组',
          lock: false,
          showTooltip: true,
          renderer: props => {
            const { record } = props;
            if (record?.get('isGrandTotal')) {
              return <span style={{ color: '#000', fontWeight: 'bold' }}></span>;
            }
            return record?.get('isEmptyRow') ? (
              <span style={{ color: '#000' }}>班组小计</span>
            ) : (
              <Tooltip title={record?.get('shiftTeamName')}>
                <div style={{ textAlign: 'center', width: '100%' }}>
                  {record?.get('shiftTeamName')}
                </div>
              </Tooltip>
            );
          },
          onCell: ({ record }) => {
            if (record?.get('isGrandTotal')) {
              return {
                colSpan: 0,
                hidden: true,
              };
            }
            return {};
          },
        },
      },
    ],
    [],
  );

  const handleRowClick = record => {
    setSelectedRow(record);
  };

  // 成品调整
  const handleProductAdjustClick = () => {
    if (selectedRow) {
      const data = selectedRow?.toData?.() || {};
      optionsDs.setQueryParameter(
        'shiftTeamId',
        data.shiftTeamId || data.shiftTeam || data.shiftTeamName,
      );
      optionsDs.setQueryParameter('shiftDate', data.shiftDate);
      optionsDs.setQueryParameter('shiftCode', data.shiftCode);
      optionsDs.setQueryParameter('positionUser', 'SJZCY');
      optionsDs.query();

      formDs.loadData([data]);
    }
    Modal.open({
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.productAdjust`).d('成品调整'),
      destroyOnClose: true,
      style: {
        width: 1300,
      },
      closable: true,
      children: (
        <div style={{ display: 'flex', alignContent: 'space-around' }}>
          <div>
            <Form dataSet={formDs} columns={2} id={styles.modalForm} className={styles.modalForm}>
              <TextField name="identification" />
              <DateTimePicker name="rollUpTime" />
              <DateTimePicker name="rollDownTime" />
              <TextField name="netWeight" restrict="0.1-9" />
              <TextField name="grossWeight" restrict="0.1-9" />
              <TextField name="defectInformation1" />
              <TextField name="defectInformation2" />
              <TextField name="defectInformation3" />
              <TextField name="defectInformation4" />
              <TextField name="joint1" />
              <TextField name="joint2" />
              <TextField name="volumeWidth" />
              <TextField name="coilingDiameter" />
              <Select name="selfTestOne" />
              <Select name="selfTestTwo" />
              <Select name="selfIsolationUser" labelWidth={130} />
              <Select name="inspectBy" />
              <TextField name="remark" />
            </Form>
          </div>
        </div>
      ),
      onCancel: () => {
        formDs.reset();
      },
      onOk: handleModalOk,
    });
  };

  // 回料调整
  const handleReturnAdjustClick = async () => {
    if (!selectedRow) {
      return;
    }
    const params = selectedRow?.toData?.() || {};
    const res = await request(
      `${BASIC.HMES_BASIC}/v1/${tenantId}/weighing-record-report/return-adjust/query`,
      {
        method: 'POST',
        body: params,
      },
    );
    if (res && res.success) {
      const data = res.data || res.rows?.[0] || res.content?.[0] || {};
      returnAdjustDs.loadData([
        {
          ident: data.ident,
          priQty: data.priQty,
          netWeight: data.netWeight,
          grossWeight: data.grossWeight,
        },
      ]);
      Modal.open({
        destroyOnClose: true,
        title: '回料调整',
        style: { width: 600 },
        children: (
          <Form dataSet={returnAdjustDs} columns={2}>
            <Output name="ident" />
            <Output name="priQty" />
            <Output name="netWeight" />
            <Output name="grossWeight" />
            <NumberField name="adjustQty" min={0} step={1} style={{ width: '100%' }} />
          </Form>
        ),
        onOk: async () => {
          return handleReturnAdjustOk();
        },
      });
    }
  };

  // 回料调整
  const handleReturnAdjustOk = async () => {
    const payload = {
      ...(selectedRow?.toData?.() || {}),
      ...(returnAdjustDs.toData()?.[0] || {}),
    };
    const res = await request(
      `${BASIC.HMES_BASIC}/v1/${tenantId}/weighing-record-report/return-adjust/confirm`,
      {
        method: 'POST',
        body: payload,
      },
    );
    if (res && res.success) {
      const processedData = processRollInfoData(mockRollInfoData);
      rollInfoDs.loadData(processedData);
      return true;
    }
    return false;
  };

  // 回料报工
  const handleReturnReportClick = () => {};

  const handleModalOk = async () => {
    const payload = formDs.toData()?.[0] || {};
    const res = await request(
      `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-weight-measurement-report/finished-product/adjustment/ui`,
      {
        method: 'POST',
        body: payload,
      },
    );
    if (res?.success) {
      const processedData = processRollInfoData(mockRollInfoData);
      rollInfoDs.loadData(processedData);
      return true;
    }
    return false;
  };

  // useEffect(() => {
  //   // 加载假数据并处理分组
  //   const processedData = processRollInfoData(mockRollInfoData);
  //   rollInfoDs.loadData(processedData);
  // }, []);

  useEffect(() => {
    if (queryParams && Object.keys(queryParams).length > 0) {
      const {
        dateFrom,
        dateTo,
        prodLineId,
        shiftTeamId,
        shiftTeamName,
        shiftCode,
        materialName,
        customerName,
      } = queryParams;
      rollInfoDs.setQueryParameter('dateFrom', dateFrom);
      rollInfoDs.setQueryParameter('dateTo', dateTo);
      rollInfoDs.setQueryParameter('prodLineId', prodLineId);
      rollInfoDs.setQueryParameter('shiftTeamId', shiftTeamId);
      rollInfoDs.setQueryParameter('shiftTeamName', shiftTeamName);
      rollInfoDs.setQueryParameter('shiftCode', shiftCode);
      rollInfoDs.setQueryParameter('materialName', materialName);
      rollInfoDs.setQueryParameter('customerName', customerName);
      rollInfoDs.query().then(res => {
        if (res && res?.success && res?.rows) {
          const processedData = processRollInfoData(res?.rows);
          rollInfoDs.loadData(processedData);
        }
      });
    } else {
      // 加载假数据
      // productionPlanDs.loadData(mockProductionPlanData);
    }
  }, [queryParams]);

  return (
    <div className={`${styles.tableBlock} ${styles.rollInfo}`}>
      {/* <div className={styles.tableTitle}>检斤记录表</div> */}
      <div className={styles.tableTitleLine}>
        <Button
          className={styles.powderListBtn}
          onClick={handleProductAdjustClick}
          color={ButtonColor.default}
          disabled={!selectedRow}
        >
          成品调整
        </Button>
        <Button
          className={styles.powderListBtn}
          onClick={handleReturnAdjustClick}
          color={ButtonColor.default}
          disabled={!selectedRow}
        >
          回料调整
        </Button>
        <Button
          className={styles.powderListBtn}
          onClick={handleReturnReportClick}
          color={ButtonColor.default}
        >
          回料报工
        </Button>
      </div>
      <div className={`${styles.tableContent} ${styles.tableContentTop}`}>
        <Table
          columns={columns}
          dataSet={rollInfoDs}
          border
          pagination={false}
          groups={groups as any}
          style={{ maxHeight: 500 }}
          onRow={({ record }) => ({
            onClick: () => {
              if (record.get('isEmptyRow')) {
                return;
              }
              handleRowClick(record);
            },
            className:
              record.get('isSummary') || record.get('className')
                ? record.get('isSummary')
                  ? styles.summaryRow
                  : record.get('className')
                : '',
          })}
        />
      </div>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.hmes.weighingrecordreport', 'tarzan.common'],
})(
  withProps(
    () => {
      const rollInfoDs = new DataSet({
        ...rollInfoDS(),
      });
      return {
        rollInfoDs,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(RollInfoTable),
);
